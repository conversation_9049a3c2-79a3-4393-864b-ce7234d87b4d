"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { useUserWithData } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeUserAIUsage } from "@/lib/domains/user-ai-usage/user-ai-usage.realtime.hooks"
import { AIUsageCategory } from "@/lib/domains/user-ai-usage/user-ai-usage.types"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription/user-subscription.hooks"

import { AIUsageWarning } from "@/components/ai-usage-warning"
import {
  Sparkles,
  Plus,
  ExternalLink,
  RefreshCw,
  Star,
  DollarSign,
  MapPin,
  Phone,
  Globe,
  Copy,
} from "lucide-react"
import { type ItinerarySuggestion } from "@/lib/openai"
import { type Trip } from "@/lib/domains/trip/trip.types"
import { ItineraryItem } from "@/lib/domains/itinerary/itinerary.types"
import { useAIItinerarySuggestions } from "@/lib/domains/ai-suggestions/ai-suggestions-itinerary.hooks"

interface ItinerarySuggestionsProps {
  trip: Trip
  day: number
  existingActivities: ItineraryItem[]
  onActivitySelected: (activity: ItinerarySuggestion) => void
}

// Helper function to display price level
const getPriceLevelDisplay = (priceLevel?: number) => {
  if (priceLevel === undefined || priceLevel === null) return null

  const levels = {
    0: "Free",
    1: "$",
    2: "$$",
    3: "$$$",
    4: "$$$$",
  }

  return levels[priceLevel as keyof typeof levels] || null
}

// Helper function to display rating with stars
const RatingDisplay = ({
  rating,
  userRatingsTotal,
}: {
  rating?: number
  userRatingsTotal?: number
}) => {
  if (!rating) return null

  return (
    <div className="flex items-center gap-1 text-sm">
      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
      <span className="font-medium">{rating.toFixed(1)}</span>
      {userRatingsTotal && <span className="text-muted-foreground">({userRatingsTotal})</span>}
    </div>
  )
}

// Helper function to copy text to clipboard
const copyToClipboard = async (text: string, toast: any) => {
  try {
    await navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "Location address has been copied for easy directions",
    })
  } catch (error) {
    console.error("Failed to copy to clipboard:", error)
    toast({
      title: "Copy failed",
      description: "Unable to copy to clipboard. Please copy manually.",
      variant: "destructive",
    })
  }
}

export function ItinerarySuggestions({
  trip,
  day,
  existingActivities,
  onActivitySelected,
}: ItinerarySuggestionsProps) {
  const { userData } = useUserWithData()
  const { toast } = useToast()
  const isSubscribed = useIsUserSubscribed()
  const { getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)

  // Use the new AI itinerary suggestions hook
  const {
    suggestions,
    loading,
    usageError,
    showSuggestions,
    usingCachedSuggestions,
    affiliateLinks,
    loadSuggestions,
  } = useAIItinerarySuggestions(trip, day, existingActivities, userData)

  const handleSelectActivity = (suggestion: ItinerarySuggestion) => {
    onActivitySelected(suggestion)

    toast({
      title: "Activity selected",
      description: `"${suggestion.title}" has been added to your itinerary`,
    })
  }
  const title = usingCachedSuggestions ? "Cached AI Activities" : "AI Activities Suggestions"
  const description = showSuggestions
    ? !loading
      ? `Here are some ${usingCachedSuggestions ? "cached" : "suggested"} activities for day ${day} of your trip to ${trip.destination}`
      : "Getting New Suggestions..."
    : "Get personalized activity suggestions for your trip"

  return (
    <>
      {usageError && usageError.show && (
        <AIUsageWarning
          errorType={usageError.errorType}
          usageData={usageError.usageData}
          onClose={() => {}}
          className="mb-4"
        />
      )}
      <Card id="ai-activities-suggestions">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            {title}
            <div className="flex items-center ml-auto gap-2">
              {!isSubscribed && (
                <span className="text-xs text-muted-foreground">
                  {getCategoryUsage(AIUsageCategory.ITINERARY)?.count || 0}/
                  {getCategoryUsage(AIUsageCategory.ITINERARY)?.limit || 3}
                </span>
              )}
              {usingCachedSuggestions && (
                <Badge variant="outline" className="text-xs bg-primary/10 border-primary/20">
                  Cached
                </Badge>
              )}
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => loadSuggestions(true)}
                disabled={loading}
                title="Refresh AI suggestions"
              >
                {showSuggestions && (
                  <RefreshCw
                    className={`h-4 w-4 ${showSuggestions && loading ? "animate-spin" : ""}`}
                  />
                )}
              </Button>
            </div>
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {suggestions.map((suggestion, index) => {
            const affiliateLink = affiliateLinks[suggestion.title]
            const googlePlaces = suggestion.googlePlaces
            const priceLevel = getPriceLevelDisplay(googlePlaces?.price_level)

            return (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-medium">{suggestion.title}</h3>
                    <p className="text-sm text-muted-foreground">{suggestion.description}</p>

                    {/* Google Places Information */}
                    {googlePlaces && (
                      <div className="mt-2 space-y-1">
                        <div className="flex items-center gap-3 flex-wrap">
                          <RatingDisplay
                            rating={googlePlaces.rating}
                            userRatingsTotal={googlePlaces.user_ratings_total}
                          />
                          {priceLevel && (
                            <div className="flex items-center gap-1 text-sm">
                              <DollarSign className="h-4 w-4 text-green-600" />
                              <span className="font-medium text-green-600">{priceLevel}</span>
                            </div>
                          )}
                          {googlePlaces.opening_hours?.open_now !== undefined && (
                            <div className="flex items-center gap-1 text-sm">
                              <div
                                className={`h-2 w-2 rounded-full ${googlePlaces.opening_hours.open_now ? "bg-green-500" : "bg-red-500"}`}
                              />
                              <span
                                className={
                                  googlePlaces.opening_hours.open_now
                                    ? "text-green-600"
                                    : "text-red-600"
                                }
                              >
                                {googlePlaces.opening_hours.open_now ? "Open now" : "Closed"}
                              </span>
                            </div>
                          )}
                        </div>

                        {(googlePlaces.formatted_address || suggestion.location) && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <MapPin className="h-3 w-3" />
                            <span className="flex-1">
                              {googlePlaces.formatted_address || suggestion.location}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 hover:bg-muted"
                              onClick={() =>
                                copyToClipboard(
                                  googlePlaces.formatted_address || suggestion.location || "",
                                  toast
                                )
                              }
                              title="Copy address for directions"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Display multiple affiliate links if available, otherwise fallback to single link */}
                {suggestion.affiliateLinks && suggestion.affiliateLinks.length > 0 ? (
                  <div className="space-y-2 mt-2">
                    {suggestion.affiliateLinks.map((link, linkIndex) => (
                      <div key={linkIndex} className="bg-muted/50 p-3 rounded-md">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="text-sm font-medium">{link.title}</h4>
                            <p className="text-xs text-muted-foreground">{link.description}</p>
                          </div>
                          <Button variant="outline" size="sm" asChild>
                            <a
                              href={link.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1"
                            >
                              <span>{link.provider || "Visit"}</span>
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : affiliateLink ? (
                  <div className="bg-muted/50 p-3 rounded-md mt-2">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="text-sm font-medium">{affiliateLink.title}</h4>
                        <p className="text-xs text-muted-foreground">{affiliateLink.description}</p>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={affiliateLink.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-1"
                        >
                          <span>{(affiliateLink as any).provider || "Visit"}</span>
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      </Button>
                    </div>
                  </div>
                ) : null}

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full mt-2"
                  onClick={() => handleSelectActivity(suggestion)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add to Itinerary
                </Button>
              </div>
            )
          })}

          <Button
            variant="outline"
            className="w-full"
            onClick={() => loadSuggestions(true)}
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span className="text-xs sm:text-sm">
                  {showSuggestions
                    ? "Getting New suggestions..."
                    : "Finding activity suggestions..."}
                </span>
              </div>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                {!isSubscribed && (
                  <span className="mr-2 text-xs">
                    {getCategoryUsage(AIUsageCategory.ITINERARY)?.count || 0}/
                    {getCategoryUsage(AIUsageCategory.ITINERARY)?.limit || 3}
                  </span>
                )}
                {showSuggestions ? "Get New Personalized Suggestions" : "Get Activity Suggestions"}
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </>
  )
}
