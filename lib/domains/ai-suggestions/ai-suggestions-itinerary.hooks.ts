"use client"

import { useState, useCallback } from "react"
import { useIsUserSubscribed } from "../user-subscription/user-subscription.hooks"
import { useRealtimeUserAIUsage } from "../user-ai-usage/user-ai-usage.realtime.hooks"
import { UserAIUsageService } from "../user-ai-usage/user-ai-usage.service"
import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"
import { useAISuggestionsCache } from "./ai-suggestions-cache.hooks"
import { CachedItinerarySuggestion } from "./ai-suggestions-cache.service"
import { ItinerarySuggestionsHookReturn } from "./ai-suggestions.types"
import { SubscriptionErrorType } from "../user-subscription/user-subscription.types"
import { generateActivitySuggestions } from "@/lib/api-client"
import { useActivityPreferences } from "../activity-preferences/activity-preferences.hooks"
import { generateAffiliateTagsFromPreferences } from "../activity-preferences/activity-preferences.types"
import { findMultipleAffiliateLinks, TaggedAffiliateLink } from "@/lib/affiliate-links-map"
import { Trip } from "../trip/trip.types"
import { ItineraryItem } from "../itinerary/itinerary.types"
import { User } from "../user/user.types"
import { useUserPreferences } from "../user-preferences/user-preferences.hooks"
import { toast } from "@/components/ui/use-toast"

/**
 * Hook for AI itinerary suggestions
 * @param trip The trip to generate suggestions for
 * @param day The day to generate suggestions for
 * @param existingActivities Existing activities to avoid duplicates
 * @param userData The current user data
 * @returns Itinerary suggestions hook return object
 */
export function useAIItinerarySuggestions(
  trip: Trip,
  day: number,
  existingActivities: ItineraryItem[],
  userData: User | null
): ItinerarySuggestionsHookReturn {
  const isSubscribed = useIsUserSubscribed()
  const { usage, getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)
  const {
    hasCachedSuggestions,
    getItinerarySuggestions,
    saveItinerarySuggestions,
    addSuggestionsToHistory,
    getRecentSuggestionTitles,
  } = useAISuggestionsCache()
  const { preferences } = useUserPreferences(userData?.uid || "")
  const { preferences: activityPreferences } = useActivityPreferences(userData?.uid)

  // State for suggestions
  const [suggestions, setSuggestions] = useState<CachedItinerarySuggestion[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [usingCachedSuggestions, setUsingCachedSuggestions] = useState(false)
  const [affiliateLinks, setAffiliateLinks] = useState<Record<string, TaggedAffiliateLink | null>>(
    {}
  )
  const [usageError, setUsageError] = useState<ItinerarySuggestionsHookReturn["usageError"]>(null)

  /**
   * Check if the user can make an AI request
   * @param category AI usage category
   * @returns Whether the user can make a request
   */
  const canMakeRequest = useCallback(
    async (category: AIUsageCategory): Promise<boolean> => {
      if (!userData?.uid) return false

      // If user has a subscription, they can always make requests
      if (isSubscribed) return true

      // Check if the user has reached their limit
      const categoryUsage = getCategoryUsage(category)
      if (!categoryUsage) return true

      // If they've reached the limit, show an error
      if (categoryUsage.count >= categoryUsage.limit) {
        setUsageError({
          show: true,
          errorType: SubscriptionErrorType.ITINERARY_AI_LIMIT_REACHED,
          usageData: {
            daily: usage?.daily || 0,
            weekly: usage?.weekly || 0,
            dailyLimit: usage?.dailyLimit || 10,
            weeklyLimit: usage?.weeklyLimit || 50,
            categoryCount: categoryUsage.count,
            categoryLimit: categoryUsage.limit,
          },
        })
        return false
      }

      return true
    },
    [userData, isSubscribed, getCategoryUsage, usage, setUsageError]
  )

  /**
   * Load itinerary suggestions
   * @param refresh Whether to force a refresh (bypass cache)
   */
  const loadSuggestions = useCallback(
    async (refresh: boolean = false): Promise<void> => {
      if (!trip || !userData) {
        toast({
          title: "Error",
          description: "Unable to load suggestions. Please try again.",
          variant: "destructive",
        })
        return
      }

      try {
        setLoading(true)

        // If we already have suggestions and we're not refreshing, just show them
        if (suggestions.length > 0 && !refresh) {
          setShowSuggestions(true)
          setLoading(false)
          return
        }

        // Always set showSuggestions to true when loading suggestions
        setShowSuggestions(true)

        // Check if we have cached suggestions and it's not a refresh request
        if (
          !refresh &&
          userData.uid &&
          hasCachedSuggestions(AIUsageCategory.ITINERARY, trip.id, userData.uid)
        ) {
          // Get cached suggestions
          const cachedSuggestions = getItinerarySuggestions(trip.id, userData.uid)

          if (cachedSuggestions && cachedSuggestions.length > 0) {
            // Filter suggestions for the current day
            const filteredCachedSuggestions = cachedSuggestions.filter(
              (suggestion) => suggestion.day === day
            )

            if (filteredCachedSuggestions.length > 0) {
              // Set the filtered suggestions
              setSuggestions(filteredCachedSuggestions)

              // Get affiliate links for each suggestion
              const links: Record<string, TaggedAffiliateLink | null> = {}
              for (const suggestion of filteredCachedSuggestions) {
                if (suggestion.affiliateLink) {
                  links[suggestion.title] = {
                    ...suggestion.affiliateLink,
                    tags: suggestion.affiliateLink.tags || [],
                    provider: suggestion.affiliateLink.provider || "",
                  } as TaggedAffiliateLink
                } else {
                  links[suggestion.title] = null
                }
              }
              setAffiliateLinks(links)

              setUsingCachedSuggestions(true)
              setLoading(false)
              return
            }
          }
        }

        // If we're here, we need to make a new request
        setUsingCachedSuggestions(false)

        // Check if the user can make a request
        const canMakeAIRequest = await canMakeRequest(AIUsageCategory.ITINERARY)
        if (!canMakeAIRequest) {
          // Even if the user can't make a new request, check for cached suggestions
          // This ensures users can still see cached suggestions when they've reached their limit
          if (
            userData.uid &&
            hasCachedSuggestions(AIUsageCategory.ITINERARY, trip.id, userData.uid)
          ) {
            const cachedSuggestions = getItinerarySuggestions(trip.id, userData.uid)

            if (cachedSuggestions && cachedSuggestions.length > 0) {
              // Filter suggestions for the current day
              const filteredCachedSuggestions = cachedSuggestions.filter(
                (suggestion) => suggestion.day === day
              )

              if (filteredCachedSuggestions.length > 0) {
                // Set the filtered suggestions
                setSuggestions(filteredCachedSuggestions)

                // Get affiliate links for each suggestion
                const links: Record<string, TaggedAffiliateLink | null> = {}
                for (const suggestion of filteredCachedSuggestions) {
                  if (suggestion.affiliateLink) {
                    links[suggestion.title] = {
                      ...suggestion.affiliateLink,
                      tags: suggestion.affiliateLink.tags || [],
                      provider: suggestion.affiliateLink.provider || "",
                    } as TaggedAffiliateLink
                  } else {
                    links[suggestion.title] = null
                  }
                }
                setAffiliateLinks(links)

                setUsingCachedSuggestions(true)
                setLoading(false)
                return
              }
            }
          }

          // If no cached suggestions found, just stop loading
          setLoading(false)
          return
        }

        // Get existing activity titles for filtering
        const existingTitles = existingActivities.map((activity) => activity.title.toLowerCase())

        // Generate activity suggestions
        // Combine user data with preferences
        const userPrefs = userData
          ? {
              ...userData,
              email: userData.email || "", // Convert string | null to string | undefined
              travelPreferences: preferences?.travelPreferences || [],
              budgetRange: preferences?.budgetRange || [500, 2000],
              preferredTravelSeasons: preferences?.preferredTravelSeasons || [],
              availabilityPreferences: preferences?.availabilityPreferences || [],
            }
          : {}

        // Get recent suggestions for deduplication (Pro users only)
        const recentSuggestions =
          isSubscribed && userData?.uid ? getRecentSuggestionTitles(trip.id, day, 3) : []

        // Use activity preferences for Pro users only
        const activityPrefs = isSubscribed ? activityPreferences : undefined

        const activitySuggestions = await generateActivitySuggestions(
          trip,
          userPrefs,
          day,
          activityPrefs,
          recentSuggestions
        )

        // Filter out suggestions that are similar to existing activities
        const filteredSuggestions = activitySuggestions.filter((suggestion) => {
          const title = suggestion.title.toLowerCase()
          return !existingTitles.some((existingTitle) => {
            return title.includes(existingTitle) || existingTitle.includes(title)
          })
        })

        // Take only the top 3 suggestions
        const topSuggestions = filteredSuggestions.slice(0, 3)

        // Get affiliate links for each suggestion using the affiliate links map (up to 2 per suggestion)
        const linksMap: Record<string, TaggedAffiliateLink[]> = {}
        const legacyLinks: Record<string, TaggedAffiliateLink | null> = {}

        for (const suggestion of topSuggestions) {
          // Generate enhanced tags from activity preferences for Pro users
          let enhancedTags = suggestion.tags || []
          if (isSubscribed && activityPreferences) {
            const preferenceTags = generateAffiliateTagsFromPreferences(activityPreferences)
            enhancedTags = [...enhancedTags, ...preferenceTags]
          }

          // Find multiple matching affiliate links using enhanced tags
          const affiliateLinks = findMultipleAffiliateLinks(
            suggestion.title,
            suggestion.description,
            2, // max 2 links per suggestion
            trip.destination,
            enhancedTags // pass enhanced tags including preference-based tags
          )
          linksMap[suggestion.title] = affiliateLinks
          // Keep legacy single link for backward compatibility
          legacyLinks[suggestion.title] = affiliateLinks.length > 0 ? affiliateLinks[0] : null
        }
        setAffiliateLinks(legacyLinks)

        // Convert to cached format with Google Places data
        const cachedSuggestions: CachedItinerarySuggestion[] = topSuggestions.map((suggestion) => ({
          title: suggestion.title,
          description: suggestion.description,
          day: suggestion.day,
          timeOfDay: suggestion.timeOfDay,
          duration: suggestion.duration,
          location: suggestion.location,
          tags: suggestion.tags,
          affiliateLinks: linksMap[suggestion.title],
          // Keep legacy single link for backward compatibility
          affiliateLink: legacyLinks[suggestion.title],
          // Include Google Places data if available
          googlePlaces: suggestion.googlePlaces,
        }))

        // Cache the suggestions if we have a user ID
        if (userData?.uid) {
          saveItinerarySuggestions(trip.id, userData.uid, cachedSuggestions)

          // Add suggestions to history for deduplication (Pro users only)
          if (isSubscribed) {
            const suggestionTitles = cachedSuggestions.map((s) => s.title)
            addSuggestionsToHistory(trip.id, suggestionTitles, day)
          }
        }

        // Only increment the user's AI usage counter if we successfully generated and processed suggestions
        // and we're not using cached suggestions
        if (!usingCachedSuggestions) {
          await UserAIUsageService.incrementAIUsage(userData.uid, AIUsageCategory.ITINERARY)
        }

        setSuggestions(cachedSuggestions)
      } catch (err) {
        console.error("Error loading itinerary suggestions:", err)
        setError("Failed to load itinerary suggestions. Please try again.")
        toast({
          title: "Error",
          description: "Failed to load itinerary suggestions. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    },
    [
      trip,
      day,
      userData,
      preferences,
      existingActivities,
      suggestions,
      hasCachedSuggestions,
      getItinerarySuggestions,
      saveItinerarySuggestions,
      canMakeRequest,
      usingCachedSuggestions,
    ]
  )

  return {
    suggestions,
    loading,
    error,
    usageError,
    showSuggestions,
    usingCachedSuggestions,
    affiliateLinks,
    loadSuggestions,
    canMakeRequest,
  }
}
